class FlowFreeSolver:
    def __init__(self, rows, cols, pairs):
        """
        Initialize the Flow Free solver.

        Args:
            rows (int): Number of rows in the grid
            cols (int): Number of columns in the grid
            pairs (dict): Dictionary where key is the symbol and value is tuple of two coordinates
                         Example: {1: ((0,0), (3,5)), 2: ((2,3), (6,2))}
        """
        self.rows = rows
        self.cols = cols
        self.pairs = pairs
        self.grid = [[0 for _ in range(cols)] for _ in range(rows)]
        self.paths = {}  # Store the path for each pair

        # Debug flags for bad shape detection
        self.debug = {
            'check_2x2_square': True,
            'check_isolated_cell': True,
            'check_zigzag': True,
            'check_trapped_endpoint': True
        }

        # Mark the endpoints in the grid
        for symbol, (start, end) in pairs.items():
            self.grid[start[0]][start[1]] = symbol
            self.grid[end[0]][end[1]] = symbol

    def get_neighbors(self, row, col):
        """Get valid neighboring cells (cardinal directions only)."""
        directions = [(0, 1), (1, 0), (0, -1), (-1, 0)]  # right, down, left, up
        neighbors = []

        for dr, dc in directions:
            new_row, new_col = row + dr, col + dc
            if 0 <= new_row < self.rows and 0 <= new_col < self.cols:
                neighbors.append((new_row, new_col))

        return neighbors

    def get_ordered_neighbors(self, row, col, symbol, path):
        neighbors = self.get_neighbors(row, col)
        target_end = self.pairs[symbol][1]
        if len(path) == 1:
            required_move = self.get_required_first_move(symbol)
            if required_move and required_move in neighbors:
                return [required_move]
        ordered = []
        if target_end in neighbors:
            ordered.append(target_end)
            neighbors.remove(target_end)
        good_shape_neighbors = []
        edge_neighbors = []
        regular_neighbors = []
        for nr, nc in neighbors:
            # Skip neighbors that would create bad shapes
            temp_path = path + [(nr, nc)]
            if self.has_bad_shape(temp_path, symbol):
                continue
            
            if self.creates_good_shape(nr, nc, symbol):
                good_shape_neighbors.append((nr, nc))
            elif self.is_edge(nr, nc):
                edge_neighbors.append((nr, nc))
            else:
                regular_neighbors.append((nr, nc))
        ordered.extend(good_shape_neighbors)
        ordered.extend(edge_neighbors)
        ordered.extend(regular_neighbors)
        return ordered

    def get_required_first_move(self, symbol):
        """
        Get the required first move for a symbol based on heuristic rules.
        Returns the required next position or None
        """
        start, _ = self.pairs[symbol]
        if self.is_edge(start[0], start[1]):
            is_near, corner = self.is_near_corner(start[0], start[1])
            if is_near:
                return corner
        return None

    def would_have_eight_empty_neighbors(self, center_row, center_col, new_row, new_col, symbol):
        """
        Check if a center position would have 8 empty neighbors if we place symbol at (new_row, new_col).
        This checks all 8 directions around the center (including diagonals).
        """
        # All 8 directions: N, NE, E, SE, S, SW, W, NW
        directions = [(-1, 0), (-1, 1), (0, 1), (1, 1), (1, 0), (1, -1), (0, -1), (-1, -1)]

        empty_count = 0
        for dr, dc in directions:
            check_row, check_col = center_row + dr, center_col + dc

            # Skip if out of bounds
            if not (0 <= check_row < self.rows and 0 <= check_col < self.cols):
                continue

            # Check what would be at this position
            if (check_row, check_col) == (new_row, new_col):
                continue  # This would be filled by our new move, so not empty
            elif self.grid[check_row][check_col] == 0:
                empty_count += 1

        # Return True if we'd have many empty neighbors (6+ is good, 8 is ideal but rare)
        return empty_count >= 6

    def creates_good_shape(self, row, col, symbol):
        """
        Check if moving to this position creates a good shape.
        A good shape is when a node (part of current path) has 8 empty neighbors around it.
        This creates flexible space for other paths to navigate around.
        """
        # First, check if placing our symbol here would create the good pattern
        # We need to temporarily place the symbol and check if any existing path nodes
        # now have 8 empty neighbors

        # Get all positions that will be part of our path if we make this move
        current_path_positions = set()
        for other_symbol, other_path in self.paths.items():
            if other_symbol != symbol:
                current_path_positions.update(other_path[1:-1])  # Exclude endpoints

        # Check if making this move would create a node with 8 empty neighbors
        # Look at all current path positions and see if any would benefit
        for path_row, path_col in current_path_positions:
            if self.would_have_eight_empty_neighbors(path_row, path_col, row, col, symbol):
                return True

        # Also check if the new position itself might create a good central hub
        # (though this is less common since we're building the path)
        return False

    def is_corner(self, row, col):
        """Check if position is a corner of the grid."""
        return ((row == 0 or row == self.rows - 1) and
                (col == 0 or col == self.cols - 1))

    def is_edge(self, row, col):
        """Check if position is on the edge of the grid."""
        return (row == 0 or row == self.rows - 1 or
                col == 0 or col == self.cols - 1)

    def is_near_corner(self, row, col):
        """Check if position is one space away from a corner."""
        corners = [(0, 0), (0, self.cols - 1), (self.rows - 1, 0), (self.rows - 1, self.cols - 1)]
        for corner_row, corner_col in corners:
            if (abs(row - corner_row) == 1 and col == corner_col) or \
                    (abs(col - corner_col) == 1 and row == corner_row):
                return True, (corner_row, corner_col)
        return False, None

    def is_valid_move(self, row, col, symbol):
        """Check if a move to (row, col) is valid for the given symbol."""
        # Check bounds
        if row < 0 or row >= self.rows or col < 0 or col >= self.cols:
            return False

        # Check if cell is empty or is the target endpoint
        target_end = self.pairs[symbol][1]
        if self.grid[row][col] == 0 or (row, col) == target_end:
            return True

        return False

    def is_edge_preferred_move(self, current_pos, next_pos):
        """
        Check if a move from current_pos to next_pos follows edge-preferred routing.
        Prioritizes staying on edges when possible.
        """
        current_row, current_col = current_pos
        next_row, next_col = next_pos

        # If we're currently on an edge, prefer to stay on edges
        if self.is_edge(current_row, current_col):
            return self.is_edge(next_row, next_col)

        # If we're not on an edge, moving to an edge is still good
        return self.is_edge(next_row, next_col)

    def has_bad_shape(self, path, symbol):
        """
        Check if the current path contains any "bad shapes" that make the puzzle unsolvable.
        Returns True if a bad shape is detected.
        """
        if len(path) < 3:
            return False

        # Get the last position
        current = path[-1]

        print(f"  🔍 Checking for bad shapes at {current} for path of length {len(path)}")

        # Bad Shape 1: Creating a 2x2 square (very common bad pattern)
        if self.debug['check_2x2_square'] and len(path) >= 4:
            square_result = self.creates_2x2_square(path[-4:])
            if square_result:
                print(f"  ❌ Bad shape detected: 2x2 square at {path[-4:]}")
                return True

        # Bad Shape 2: Creating isolated single cells
        if self.debug['check_isolated_cell']:
            isolated_result = self.creates_isolated_cell(current, symbol)
            if isolated_result:
                print(f"  ❌ Bad shape detected: isolated cell near {current}")
                return True

        # Bad Shape 3: Unnecessary zigzag patterns
        if self.debug['check_zigzag'] and len(path) >= 4:
            zigzag_result = self.creates_zigzag(path[-4:])
            if zigzag_result:
                print(f"  ❌ Bad shape detected: zigzag pattern at {path[-4:]}")
                return True

        # Bad Shape 4: Trapping other endpoints
        if self.debug['check_trapped_endpoint']:
            trapped_result = self.traps_endpoint(current, symbol)
            if trapped_result:
                print(f"  ❌ Bad shape detected: trapping endpoint at {current}")
                return True

        print(f"  ✅ No bad shapes detected at {current}")
        return False

    def creates_2x2_square(self, recent_path):
        """Check if recent path creates a 2x2 square pattern."""
        if len(recent_path) < 4:
            return False

        # Get the 4 most recent positions
        positions = recent_path[-4:]
        print(f"    🔍 2x2 square check: Examining points {positions}")

        # Check if they form a 2x2 square
        rows = [pos[0] for pos in positions]
        cols = [pos[1] for pos in positions]

        min_row, max_row = min(rows), max(rows)
        min_col, max_col = min(cols), max(cols)

        # If they span exactly 2 rows and 2 columns, it might be a 2x2 square
        if max_row - min_row == 1 and max_col - min_col == 1:
            expected_square = {(min_row, min_col), (min_row, max_col),
                               (max_row, min_col), (max_row, max_col)}
            is_square = set(positions[-4:]) == expected_square
            if is_square:
                print(f"    ❌ Detected 2x2 square: {positions}")
            return is_square

        return False

    def creates_isolated_cell(self, current_pos, symbol):
        """Check if current position creates an isolated cell that can't be filled."""
        row, col = current_pos
        
        # Check all neighbors of the current position
        for dr, dc in [(0, 1), (1, 0), (0, -1), (-1, 0)]:
            neighbor_row, neighbor_col = row + dr, col + dc
            
            # Skip if out of bounds or not empty
            if not (0 <= neighbor_row < self.rows and 0 <= neighbor_col < self.cols):
                continue
            if self.grid[neighbor_row][neighbor_col] != 0:
                continue
                
            # Count empty neighbors of this neighbor
            empty_count = 0
            for ndr, ndc in [(0, 1), (1, 0), (0, -1), (-1, 0)]:
                check_row, check_col = neighbor_row + ndr, neighbor_col + ndc
                if 0 <= check_row < self.rows and 0 <= check_col < self.cols:
                    if (check_row, check_col) == current_pos:
                        continue  # Skip the current position
                    if self.grid[check_row][check_col] == 0:
                        empty_count += 1
                        
            # Only consider it isolated if it has NO empty neighbors (completely surrounded)
            # Having 1 empty neighbor is fine - the path can reach it from that direction
            if empty_count == 0:
                is_endpoint = False
                for other_symbol, (start, end) in self.pairs.items():
                    if other_symbol not in self.paths:  # Only check unsolved pairs
                        if (neighbor_row, neighbor_col) == start or (neighbor_row, neighbor_col) == end:
                            is_endpoint = True
                            break

                # If not an endpoint and has no other empty neighbors, it would be completely isolated
                if not is_endpoint:
                    print(f"    ❌ Would create completely isolated cell at ({neighbor_row}, {neighbor_col})")
                    return True
                
        return False

    def creates_zigzag(self, recent_path):
        """Check for unnecessary zigzag patterns that waste space."""
        if len(recent_path) < 4:
            return False

        # Check if we're making a zigzag: A -> B -> C -> D where A and C are aligned, B and D are aligned
        a, b, c, d = recent_path[-4:]

        print(f"    🔍 Zigzag check: Examining points {a}, {b}, {c}, {d}")

        # Horizontal zigzag: A-C same row, B-D same row, different rows
        if (a[0] == c[0] and b[0] == d[0] and a[0] != b[0] and
                abs(a[1] - c[1]) == 2 and abs(b[1] - d[1]) == 2):
            print(f"    ❌ Detected horizontal zigzag: {a}, {b}, {c}, {d}")
            return True

        # Vertical zigzag: A-C same col, B-D same col, different cols
        if (a[1] == c[1] and b[1] == d[1] and a[1] != b[1] and
                abs(a[0] - c[0]) == 2 and abs(b[0] - d[0]) == 2):
            print(f"    ❌ Detected vertical zigzag: {a}, {b}, {c}, {d}")
            return True

        return False

    def traps_endpoint(self, current_pos, current_symbol):
        """Check if current position traps an endpoint of another unsolved pair."""
        # For each unsolved pair, check if we're blocking access to its endpoints
        for symbol, (start, end) in self.pairs.items():
            if symbol == current_symbol or symbol in self.paths:
                continue  # Skip current symbol and already solved pairs

            # Check if we're trapping either endpoint
            for endpoint in [start, end]:
                if self.would_trap_position(endpoint, current_pos):
                    print(f"    ❌ Would trap endpoint {endpoint} of symbol {symbol}")
                    return True

        return False

    def would_trap_position(self, target_pos, blocking_pos):
        """Check if placing something at blocking_pos would trap target_pos."""
        target_row, target_col = target_pos

        # Count accessible neighbors from target position
        accessible_neighbors = 0
        for dr, dc in [(0, 1), (1, 0), (0, -1), (-1, 0)]:
            new_row, new_col = target_row + dr, target_col + dc
            if 0 <= new_row < self.rows and 0 <= new_col < self.cols:
                if (new_row, new_col) == blocking_pos:
                    continue  # This would be blocked
                if self.grid[new_row][new_col] == 0:
                    accessible_neighbors += 1

        # Only consider it trapped if it would have NO accessible neighbors (completely surrounded)
        # Having 1 accessible neighbor is still okay - the path can come from that direction
        trapped = accessible_neighbors == 0
        if trapped:
            print(f"    ⚠️  Position {target_pos} would be completely trapped with {accessible_neighbors} accessible neighbors")
        return trapped

    def find_path(self, symbol, start, end, path, visited):
        """
        Find a path from start to end for the given symbol using backtracking.

        Args:
            symbol: The symbol/number we're connecting
            start: Starting coordinates (row, col)
            end: Ending coordinates (row, col)
            path: Current path being built
            visited: Set of visited coordinates for this path

        Returns:
            List of coordinates representing the path, or None if no path found
        """
        row, col = start

        # If we reached the end, return the path
        if start == end:
            return path

        # Try each neighbor
        neighbors = self.get_neighbors(row, col)
        print(f"  📍 Symbol {symbol}: Exploring from {start}, neighbors: {neighbors}")

        for next_row, next_col in neighbors:
            # Skip if already visited in this path
            if (next_row, next_col) in visited:
                print(f"    ⏭️  Skipping {(next_row, next_col)} - already visited")
                continue

            # Skip if this move is not valid
            if not self.is_valid_move(next_row, next_col, symbol):
                print(f"    ⏭️  Skipping {(next_row, next_col)} - invalid move")
                continue

            # If this is not the target endpoint and the cell is occupied, skip
            if (next_row, next_col) != end and self.grid[next_row][next_col] != 0:
                print(f"    ⏭️  Skipping {(next_row, next_col)} - cell occupied")
                continue

            print(f"\n  🎯 Symbol {symbol}: Trying move to ({next_row}, {next_col})")

            # Make the move
            old_value = self.grid[next_row][next_col]
            if (next_row, next_col) != end:  # Don't overwrite the endpoint
                self.grid[next_row][next_col] = symbol

            visited.add((next_row, next_col))
            new_path = path + [(next_row, next_col)]
            self.print_grid_with_path(new_path, symbol, (next_row, next_col))

            # Check if this move creates a bad shape
            if self.has_bad_shape(new_path, symbol):
                print(f"  ⬅️  Symbol {symbol}: Move to ({next_row}, {next_col}) creates a bad shape, backtracking")
                visited.remove((next_row, next_col))
                if (next_row, next_col) != end:
                    self.grid[next_row][next_col] = old_value
                print()  # Add line break after backtracking
                continue

            # Recursively try to complete the path
            result = self.find_path(symbol, (next_row, next_col), end, new_path, visited)

            if result is not None:
                return result

            # Backtrack
            print(f"\n  ⬅️  Symbol {symbol}: Backtracking from ({next_row}, {next_col})")
            visited.remove((next_row, next_col))
            if (next_row, next_col) != end:
                self.grid[next_row][next_col] = old_value
            print()  # Add line break after backtracking

        return None

    def solve(self):
        """
        Solve the Flow Free puzzle using backtracking.

        Returns:
            True if solved successfully, False otherwise
        """
        symbols = list(self.pairs.keys())
        return self.solve_recursive(symbols, 0)

    def solve_recursive(self, symbols, index):
        """Recursive helper for solving the puzzle."""
        # Base case: all pairs connected
        if index == len(symbols):
            return self.is_complete()

        symbol = symbols[index]
        start, end = self.pairs[symbol]

        print(f"\n🔍 Solving symbol {symbol}: {start} → {end}")

        # Try to find a path for this symbol
        path = self.find_path(symbol, start, end, [start], {start})

        if path is not None:
            print(f"✅ Found path for symbol {symbol}")
            # Store the path and try to solve the rest
            self.paths[symbol] = path
            if self.solve_recursive(symbols, index + 1):
                return True

            # Backtrack: remove this path
            print(f"⬅️  Backtracking: removing path for symbol {symbol}")
            del self.paths[symbol]
            for row, col in path[1:-1]:  # Don't remove endpoints
                self.grid[row][col] = 0

        else:
            print(f"❌ No path found for symbol {symbol}")

        return False

    def is_complete(self):
        """Check if all cells in the grid are filled."""
        for row in range(self.rows):
            for col in range(self.cols):
                if self.grid[row][col] == 0:
                    return False
        return True

    def print_solution(self):
        """Print the solved grid and paths."""
        print("\n" + "="*50)
        print("🎉 SOLUTION FOUND!")
        print("="*50)
        self._print_formatted_grid(self.grid, "Final Solution")

        print("\n📍 Solution Paths:")
        print("-" * 30)
        for symbol, path in self.paths.items():
            start, end = self.pairs[symbol]
            print(f"  Symbol {symbol}: {start} → {end}")
            print(f"    Path: {' → '.join(map(str, path))}")
        print()

    def get_solution_grid(self):
        """Return the solution grid as a 2D list."""
        return [row[:] for row in self.grid]  # Return a copy

    def print_current_grid(self, title="Current Grid State"):
        """Print the current state of the grid for debugging."""
        self._print_formatted_grid(self.grid, title)

    def print_grid_with_path(self, path, symbol, current_pos=None):
        """Print the current grid with the current path highlighted."""
        temp_grid = [row[:] for row in self.grid]

        # Mark the path with a special character
        for r, c in path:
            if temp_grid[r][c] == 0:  # Don't overwrite endpoints
                temp_grid[r][c] = symbol  # Mark path with symbol

        title = f"Exploring Symbol {symbol} → {current_pos if current_pos else 'path'}"
        self._print_formatted_grid_with_highlight(temp_grid, title, current_pos, symbol)

    def _print_formatted_grid(self, grid, title="Grid", compact=False):
        """Print a nicely formatted grid with borders and proper alignment."""
        if not compact:
            print(f"\n┌─ {title} ─" + "─" * max(0, 40 - len(title)) + "┐")
        else:
            print(f"  {title}:")

        # Calculate the width needed for each cell
        max_width = 3
        for row in grid:
            for cell in row:
                cell_str = str(cell) if cell != 0 else '·'
                max_width = max(max_width, len(cell_str))

        # Print the grid with proper formatting
        for i, row in enumerate(grid):
            if not compact:
                line = "│ "
            else:
                line = "    "

            for j, cell in enumerate(row):
                cell_str = str(cell) if cell != 0 else '·'
                line += f"{cell_str:^{max_width}}"
                if j < len(row) - 1:
                    line += " "

            if not compact:
                line += " │"
            print(line)

        if not compact:
            print("└" + "─" * (len(line) - 1) + "┘")
        print()


def solve_flow_free(rows, cols, pairs):
    """
    Convenience function to solve a Flow Free puzzle.

    Args:
        rows (int): Number of rows
        cols (int): Number of columns
        pairs (dict): Dictionary of symbol -> ((start_row, start_col), (end_row, end_col))

    Returns:
        2D list representing the solved grid, or None if no solution exists
    """
    solver = FlowFreeSolver(rows, cols, pairs)

    if solver.solve():
        return solver.get_solution_grid()
    else:
        return None


# Example usage and test cases
if __name__ == "__main__":
    # Test case 1: Simple 1x3 puzzle that should solve
    print("=== Test Case 1: 1x3 Puzzle ===")
    pairs1 = {
        1: ((0, 0), (0, 2))
    }

    solver1 = FlowFreeSolver(1, 3, pairs1)
    solver1.print_current_grid("Simple 1x3 Puzzle")

    print("🚀 Starting solver...")
    if solver1.solve():
        solver1.print_solution()
    else:
        print("❌ No solution found")
        solver1.print_current_grid("Final State (No Solution)")

    print("\n" + "=" * 50 + "\n")

    # Test case 2: 5x5 puzzle
    print("=== Test Case 2: 5x5 Puzzle ===")
    pairs2 = {
        1: ((0, 0), (4, 1)),
        2: ((0, 2), (3, 1)),
        3: ((1, 2), (4, 2)),
        4: ((0, 4), (3, 3)),
        5: ((1, 4), (4, 3))
    }

    solver2 = FlowFreeSolver(5, 5, pairs2)
    solver2.print_current_grid("Initial Puzzle Setup")

    print("🚀 Starting solver...")
    if solver2.solve():
        solver2.print_solution()
    else:
        print("\n❌ No solution found")
        solver2.print_current_grid("Final State (No Solution)")

    # print("\n" + "=" * 50 + "\n")
    #
    # # Example of how to use with detailed solver
    # print("=== Detailed Solution Example ===")
    # solver = FlowFreeSolver(4, 4, pairs1)
    # if solver.solve():
    #     solver.print_solution()
    # else:
    #     print(nosol)
